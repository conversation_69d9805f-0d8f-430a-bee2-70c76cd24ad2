{"FK_DefaultValue_ElementId": {"from_table": "DefaultValue", "to_table": "Element", "mappings": [{"ParentColumn": "ElementId", "ReferencedColumn": "Id"}]}, "FK_DefaultValue_VehicleTypeId": {"from_table": "DefaultValue", "to_table": "VehicleType", "mappings": [{"ParentColumn": "VehicleTypeId", "ReferencedColumn": "Id"}]}, "FK_EngineModelPattern_EngineModel": {"from_table": "EngineModelPattern", "to_table": "EngineModel", "mappings": [{"ParentColumn": "EngineModelId", "ReferencedColumn": "Id"}]}, "FK_Make_Model_Make": {"from_table": "Make_Model", "to_table": "Make", "mappings": [{"ParentColumn": "MakeId", "ReferencedColumn": "Id"}]}, "FK_Make_Model_Model": {"from_table": "Make_Model", "to_table": "Model", "mappings": [{"ParentColumn": "ModelId", "ReferencedColumn": "Id"}]}, "FK_Pattern_Element": {"from_table": "Pattern", "to_table": "Element", "mappings": [{"ParentColumn": "ElementId", "ReferencedColumn": "Id"}]}, "FK_Pattern_VinSchema": {"from_table": "Pattern", "to_table": "VinSchema", "mappings": [{"ParentColumn": "VinSchemaId", "ReferencedColumn": "Id"}]}, "FK_VSpecSchema_VSpecPattern_VehicleSpecSchema": {"from_table": "VSpecSchemaPattern", "to_table": "VehicleSpecSchema", "mappings": [{"ParentColumn": "SchemaId", "ReferencedColumn": "Id"}]}, "FK_VehicleData_NotWmiRelated_Element": {"from_table": "VehicleSpecPattern", "to_table": "Element", "mappings": [{"ParentColumn": "ElementId", "ReferencedColumn": "Id"}]}, "FK_Wmi_Make_Make": {"from_table": "Wmi_Make", "to_table": "Make", "mappings": [{"ParentColumn": "MakeId", "ReferencedColumn": "Id"}]}, "FK_Wmi_Make_Wmi": {"from_table": "Wmi_Make", "to_table": "Wmi", "mappings": [{"ParentColumn": "WmiId", "ReferencedColumn": "Id"}]}, "FK_Wmi_Manufacturer": {"from_table": "Wmi", "to_table": "Manufacturer", "mappings": [{"ParentColumn": "ManufacturerId", "ReferencedColumn": "Id"}]}, "FK_Wmi_VehicleType": {"from_table": "Wmi", "to_table": "VehicleType", "mappings": [{"ParentColumn": "VehicleTypeId", "ReferencedColumn": "Id"}]}, "FK_Wmi_VinSchema_VinSchema": {"from_table": "Wmi_VinSchema", "to_table": "VinSchema", "mappings": [{"ParentColumn": "VinSchemaId", "ReferencedColumn": "Id"}]}, "FK_Wmi_VinSchema_Wmi": {"from_table": "Wmi_VinSchema", "to_table": "Wmi", "mappings": [{"ParentColumn": "WmiId", "ReferencedColumn": "Id"}]}}