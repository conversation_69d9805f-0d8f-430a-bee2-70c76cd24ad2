{"version": 2, "entries": [{"package": "patrol", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol-3.18.0/", "packageUri": "lib/", "config": {"name": "patrol", "issueTracker": "https://github.com/leancodepl/patrol/issues", "version": "1.0.1", "materialIconCodePoint": "0xea4b"}}, {"package": "shared_preferences", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/", "packageUri": "lib/", "config": {"name": "shared_preferences", "issueTracker": "https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+shared_preferences%22", "version": "1.0.0", "materialIconCodePoint": "0xe683"}}, {"package": "carnow", "rootUri": "../", "packageUri": "lib/"}]}