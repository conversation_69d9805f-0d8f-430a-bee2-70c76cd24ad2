// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleTrim _$VehicleTrimFromJson(Map<String, dynamic> json) => _VehicleTrim(
  id: (json['id'] as num).toInt(),
  modelId: (json['modelId'] as num).toInt(),
  name: json['name'] as String,
  trimLevel: json['trimLevel'] as String?,
  doors: (json['doors'] as num?)?.toInt(),
  seats: (json['seats'] as num?)?.toInt(),
  transmissionType: json['transmissionType'] as String?,
  driveType: json['driveType'] as String?,
  isCurrent: json['isCurrent'] as bool? ?? true,
  enginesCount: (json['enginesCount'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$VehicleTrimToJson(_VehicleTrim instance) =>
    <String, dynamic>{
      'id': instance.id,
      'modelId': instance.modelId,
      'name': instance.name,
      'trimLevel': instance.trimLevel,
      'doors': instance.doors,
      'seats': instance.seats,
      'transmissionType': instance.transmissionType,
      'driveType': instance.driveType,
      'isCurrent': instance.isCurrent,
      'enginesCount': instance.enginesCount,
    };

_VehicleTrimListResponse _$VehicleTrimListResponseFromJson(
  Map<String, dynamic> json,
) => _VehicleTrimListResponse(
  trims: (json['trims'] as List<dynamic>)
      .map((e) => VehicleTrim.fromJson(e as Map<String, dynamic>))
      .toList(),
  total: (json['total'] as num).toInt(),
  hasMore: json['hasMore'] as bool? ?? false,
);

Map<String, dynamic> _$VehicleTrimListResponseToJson(
  _VehicleTrimListResponse instance,
) => <String, dynamic>{
  'trims': instance.trims,
  'total': instance.total,
  'hasMore': instance.hasMore,
};
