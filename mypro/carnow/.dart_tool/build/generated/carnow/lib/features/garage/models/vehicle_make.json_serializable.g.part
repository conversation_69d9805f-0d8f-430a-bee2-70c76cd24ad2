// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleMake _$VehicleMakeFromJson(Map<String, dynamic> json) => _VehicleMake(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  country: json['country'] as String?,
  logoUrl: json['logoUrl'] as String?,
  isActive: json['isActive'] as bool? ?? true,
  modelsCount: (json['modelsCount'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$VehicleMakeToJson(_VehicleMake instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'country': instance.country,
      'logoUrl': instance.logoUrl,
      'isActive': instance.isActive,
      'modelsCount': instance.modelsCount,
    };

_VehicleMakeListResponse _$VehicleMakeListResponseFromJson(
  Map<String, dynamic> json,
) => _VehicleMakeListResponse(
  makes: (json['makes'] as List<dynamic>)
      .map((e) => VehicleMake.fromJson(e as Map<String, dynamic>))
      .toList(),
  total: (json['total'] as num).toInt(),
  hasMore: json['hasMore'] as bool? ?? false,
);

Map<String, dynamic> _$VehicleMakeListResponseToJson(
  _VehicleMakeListResponse instance,
) => <String, dynamic>{
  'makes': instance.makes,
  'total': instance.total,
  'hasMore': instance.hasMore,
};
