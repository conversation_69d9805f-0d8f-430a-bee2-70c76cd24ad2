// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserVehicle _$UserVehicleFromJson(Map<String, dynamic> json) => _UserVehicle(
  id: (json['id'] as num).toInt(),
  userId: json['userId'] as String,
  make: json['make'] as String,
  model: json['model'] as String,
  year: (json['year'] as num).toInt(),
  trim: json['trim'] as String?,
  engine: json['engine'] as String?,
  color: json['color'] as String?,
  vin: json['vin'] as String?,
  licensePlate: json['licensePlate'] as String?,
  mileage: (json['mileage'] as num?)?.toInt() ?? 0,
  condition: json['condition'] as String? ?? 'good',
  purchaseDate: json['purchaseDate'] == null
      ? null
      : DateTime.parse(json['purchaseDate'] as String),
  purchasePrice: (json['purchasePrice'] as num?)?.toDouble(),
  insuranceExpiry: json['insuranceExpiry'] == null
      ? null
      : DateTime.parse(json['insuranceExpiry'] as String),
  lastServiceDate: json['lastServiceDate'] == null
      ? null
      : DateTime.parse(json['lastServiceDate'] as String),
  nextServiceDue: json['nextServiceDue'] == null
      ? null
      : DateTime.parse(json['nextServiceDue'] as String),
  imageUrls:
      (json['imageUrls'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  isPrimary: json['isPrimary'] as bool? ?? false,
  isActive: json['isActive'] as bool? ?? true,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  makeRef: json['makeRef'] == null
      ? null
      : VehicleMake.fromJson(json['makeRef'] as Map<String, dynamic>),
  modelRef: json['modelRef'] == null
      ? null
      : VehicleModel.fromJson(json['modelRef'] as Map<String, dynamic>),
  yearRef: json['yearRef'] == null
      ? null
      : VehicleYear.fromJson(json['yearRef'] as Map<String, dynamic>),
  trimRef: json['trimRef'] == null
      ? null
      : VehicleTrim.fromJson(json['trimRef'] as Map<String, dynamic>),
  engineRef: json['engineRef'] == null
      ? null
      : VehicleEngine.fromJson(json['engineRef'] as Map<String, dynamic>),
);

Map<String, dynamic> _$UserVehicleToJson(_UserVehicle instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'trim': instance.trim,
      'engine': instance.engine,
      'color': instance.color,
      'vin': instance.vin,
      'licensePlate': instance.licensePlate,
      'mileage': instance.mileage,
      'condition': instance.condition,
      'purchaseDate': instance.purchaseDate?.toIso8601String(),
      'purchasePrice': instance.purchasePrice,
      'insuranceExpiry': instance.insuranceExpiry?.toIso8601String(),
      'lastServiceDate': instance.lastServiceDate?.toIso8601String(),
      'nextServiceDue': instance.nextServiceDue?.toIso8601String(),
      'imageUrls': instance.imageUrls,
      'isPrimary': instance.isPrimary,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'makeRef': instance.makeRef,
      'modelRef': instance.modelRef,
      'yearRef': instance.yearRef,
      'trimRef': instance.trimRef,
      'engineRef': instance.engineRef,
    };

_UserVehicleListResponse _$UserVehicleListResponseFromJson(
  Map<String, dynamic> json,
) => _UserVehicleListResponse(
  vehicles: (json['vehicles'] as List<dynamic>)
      .map((e) => UserVehicle.fromJson(e as Map<String, dynamic>))
      .toList(),
  total: (json['total'] as num).toInt(),
  hasMore: json['hasMore'] as bool? ?? false,
);

Map<String, dynamic> _$UserVehicleListResponseToJson(
  _UserVehicleListResponse instance,
) => <String, dynamic>{
  'vehicles': instance.vehicles,
  'total': instance.total,
  'hasMore': instance.hasMore,
};

_CreateUserVehicleRequest _$CreateUserVehicleRequestFromJson(
  Map<String, dynamic> json,
) => _CreateUserVehicleRequest(
  makeRefId: (json['makeRefId'] as num).toInt(),
  modelRefId: (json['modelRefId'] as num).toInt(),
  yearRefId: (json['yearRefId'] as num?)?.toInt(),
  trimRefId: (json['trimRefId'] as num?)?.toInt(),
  engineRefId: (json['engineRefId'] as num?)?.toInt(),
  color: json['color'] as String?,
  vin: json['vin'] as String?,
  licensePlate: json['licensePlate'] as String?,
  mileage: (json['mileage'] as num?)?.toInt() ?? 0,
  notes: json['notes'] as String?,
  purchaseDate: json['purchaseDate'] == null
      ? null
      : DateTime.parse(json['purchaseDate'] as String),
  purchasePrice: (json['purchasePrice'] as num?)?.toDouble(),
  insuranceExpiry: json['insuranceExpiry'] == null
      ? null
      : DateTime.parse(json['insuranceExpiry'] as String),
  lastServiceDate: json['lastServiceDate'] == null
      ? null
      : DateTime.parse(json['lastServiceDate'] as String),
  nextServiceDue: json['nextServiceDue'] == null
      ? null
      : DateTime.parse(json['nextServiceDue'] as String),
  condition: json['condition'] as String? ?? 'good',
  imageUrls:
      (json['imageUrls'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  isPrimary: json['isPrimary'] as bool? ?? false,
);

Map<String, dynamic> _$CreateUserVehicleRequestToJson(
  _CreateUserVehicleRequest instance,
) => <String, dynamic>{
  'makeRefId': instance.makeRefId,
  'modelRefId': instance.modelRefId,
  'yearRefId': instance.yearRefId,
  'trimRefId': instance.trimRefId,
  'engineRefId': instance.engineRefId,
  'color': instance.color,
  'vin': instance.vin,
  'licensePlate': instance.licensePlate,
  'mileage': instance.mileage,
  'notes': instance.notes,
  'purchaseDate': instance.purchaseDate?.toIso8601String(),
  'purchasePrice': instance.purchasePrice,
  'insuranceExpiry': instance.insuranceExpiry?.toIso8601String(),
  'lastServiceDate': instance.lastServiceDate?.toIso8601String(),
  'nextServiceDue': instance.nextServiceDue?.toIso8601String(),
  'condition': instance.condition,
  'imageUrls': instance.imageUrls,
  'isPrimary': instance.isPrimary,
};

_UpdateUserVehicleRequest _$UpdateUserVehicleRequestFromJson(
  Map<String, dynamic> json,
) => _UpdateUserVehicleRequest(
  color: json['color'] as String?,
  vin: json['vin'] as String?,
  licensePlate: json['licensePlate'] as String?,
  mileage: (json['mileage'] as num?)?.toInt(),
  notes: json['notes'] as String?,
  purchaseDate: json['purchaseDate'] == null
      ? null
      : DateTime.parse(json['purchaseDate'] as String),
  purchasePrice: (json['purchasePrice'] as num?)?.toDouble(),
  insuranceExpiry: json['insuranceExpiry'] == null
      ? null
      : DateTime.parse(json['insuranceExpiry'] as String),
  lastServiceDate: json['lastServiceDate'] == null
      ? null
      : DateTime.parse(json['lastServiceDate'] as String),
  nextServiceDue: json['nextServiceDue'] == null
      ? null
      : DateTime.parse(json['nextServiceDue'] as String),
  condition: json['condition'] as String?,
  imageUrls: (json['imageUrls'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  isPrimary: json['isPrimary'] as bool?,
  isActive: json['isActive'] as bool?,
);

Map<String, dynamic> _$UpdateUserVehicleRequestToJson(
  _UpdateUserVehicleRequest instance,
) => <String, dynamic>{
  'color': instance.color,
  'vin': instance.vin,
  'licensePlate': instance.licensePlate,
  'mileage': instance.mileage,
  'notes': instance.notes,
  'purchaseDate': instance.purchaseDate?.toIso8601String(),
  'purchasePrice': instance.purchasePrice,
  'insuranceExpiry': instance.insuranceExpiry?.toIso8601String(),
  'lastServiceDate': instance.lastServiceDate?.toIso8601String(),
  'nextServiceDue': instance.nextServiceDue?.toIso8601String(),
  'condition': instance.condition,
  'imageUrls': instance.imageUrls,
  'isPrimary': instance.isPrimary,
  'isActive': instance.isActive,
};
