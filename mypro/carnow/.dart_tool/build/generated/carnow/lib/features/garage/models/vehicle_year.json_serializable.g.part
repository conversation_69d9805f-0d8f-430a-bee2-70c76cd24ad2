// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VehicleYear _$VehicleYearFromJson(Map<String, dynamic> json) => _VehicleYear(
  id: (json['id'] as num).toInt(),
  modelId: (json['modelId'] as num).toInt(),
  year: (json['year'] as num).toInt(),
  modelName: json['modelName'] as String?,
  makeName: json['makeName'] as String?,
);

Map<String, dynamic> _$VehicleYearToJson(_VehicleYear instance) =>
    <String, dynamic>{
      'id': instance.id,
      'modelId': instance.modelId,
      'year': instance.year,
      'modelName': instance.modelName,
      'makeName': instance.makeName,
    };

_VehicleYearListResponse _$VehicleYearListResponseFromJson(
  Map<String, dynamic> json,
) => _VehicleYearListResponse(
  years: (json['years'] as List<dynamic>)
      .map((e) => VehicleYear.fromJson(e as Map<String, dynamic>))
      .toList(),
  total: (json['total'] as num).toInt(),
  hasMore: json['hasMore'] as bool? ?? false,
);

Map<String, dynamic> _$VehicleYearListResponseToJson(
  _VehicleYearListResponse instance,
) => <String, dynamic>{
  'years': instance.years,
  'total': instance.total,
  'hasMore': instance.hasMore,
};
