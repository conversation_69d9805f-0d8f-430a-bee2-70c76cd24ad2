# Design Document

## Overview

This design addresses the authentication token validation issues in the CarNow application by implementing proper JWT token handling, user ID format consistency, and database schema alignment. The solution ensures seamless authentication flow from Google OAuth through protected endpoint access.

## Architecture

### Current Authentication Flow Issues
```
Google OAuth → JWT Generation → Token Validation → Database Query
     ✅              ❌              ❌              ❌
```

### Proposed Fixed Authentication Flow
```
Google OAuth → Enhanced JWT Generation → Robust Token Validation → Consistent Database Operations
     ✅                    ✅                        ✅                          ✅
```

## Components and Interfaces

### 1. JWT Token Generation Enhancement

**Location**: `backend-go/internal/handlers/auth.go`

**Current Issue**: Token generation may not include all required claims or use inconsistent signing

**Solution**:
```go
type EnhancedJWTClaims struct {
    UserID      string `json:"user_id"`
    Email       string `json:"email"`
    Name        string `json:"name"`
    Provider    string `json:"provider"`
    IsAdmin     bool   `json:"is_admin"`
    jwt.RegisteredClaims
}

func GenerateEnhancedJWT(user *models.User) (string, error) {
    claims := EnhancedJWTClaims{
        UserID:   user.ID,
        Email:    user.Email,
        Name:     user.Name,
        Provider: "google",
        IsAdmin:  user.IsAdmin,
        RegisteredClaims: jwt.RegisteredClaims{
            Issuer:    config.JWT.Issuer,
            Subject:   user.ID,
            Audience:  []string{config.JWT.Audience},
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
            IssuedAt:  jwt.NewNumericDate(time.Now()),
            NotBefore: jwt.NewNumericDate(time.Now()),
        },
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(config.JWT.Secret))
}
```

### 2. JWT Middleware Enhancement

**Location**: `backend-go/internal/middleware/auth.go`

**Current Issue**: Token validation fails due to parsing or verification issues

**Solution**:
```go
func EnhancedJWTMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(401, gin.H{"error": "No authorization header"})
            c.Abort()
            return
        }

        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        
        token, err := jwt.ParseWithClaims(tokenString, &EnhancedJWTClaims{}, func(token *jwt.Token) (interface{}, error) {
            if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
                return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
            }
            return []byte(config.JWT.Secret), nil
        })

        if err != nil {
            log.Printf("❌ JWT Middleware: Token validation failed: %v", err)
            c.JSON(401, gin.H{"error": "Invalid token", "details": err.Error()})
            c.Abort()
            return
        }

        if claims, ok := token.Claims.(*EnhancedJWTClaims); ok && token.Valid {
            c.Set("user_id", claims.UserID)
            c.Set("user_email", claims.Email)
            c.Set("user_name", claims.Name)
            c.Set("is_admin", claims.IsAdmin)
            c.Next()
        } else {
            c.JSON(401, gin.H{"error": "Invalid token claims"})
            c.Abort()
        }
    }
}
```

### 3. User ID Format Consistency

**Location**: `backend-go/internal/models/user.go`

**Current Issue**: Google ID format conflicts with UUID expectations

**Solution**:
```go
type User struct {
    ID          string    `json:"id" gorm:"primaryKey;type:varchar(255)"`
    GoogleID    string    `json:"google_id" gorm:"uniqueIndex;type:varchar(255)"`
    Email       string    `json:"email" gorm:"uniqueIndex;not null"`
    Name        string    `json:"name" gorm:"not null"`
    Picture     string    `json:"picture"`
    IsAdmin     bool      `json:"is_admin" gorm:"default:false"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}

// Use Google ID as primary identifier
func (u *User) SetGoogleID(googleID string) {
    u.ID = googleID
    u.GoogleID = googleID
}
```

### 4. Database Query Enhancement

**Location**: `backend-go/internal/services/user_service.go`

**Current Issue**: UUID parsing error when using Google ID

**Solution**:
```go
func (s *UserService) FindOrCreateGoogleUser(googleUser *GoogleUserInfo) (*models.User, error) {
    var user models.User
    
    // Use string-based query instead of UUID parsing
    err := s.db.Where("google_id = ? OR email = ?", googleUser.ID, googleUser.Email).First(&user).Error
    
    if err == gorm.ErrRecordNotFound {
        // Create new user with Google ID as primary key
        user = models.User{
            ID:       googleUser.ID,
            GoogleID: googleUser.ID,
            Email:    googleUser.Email,
            Name:     googleUser.Name,
            Picture:  googleUser.Picture,
            IsAdmin:  s.isAdminEmail(googleUser.Email),
        }
        
        if err := s.db.Create(&user).Error; err != nil {
            return nil, fmt.Errorf("failed to create user: %w", err)
        }
    } else if err != nil {
        return nil, fmt.Errorf("failed to query user: %w", err)
    }
    
    return &user, nil
}
```

## Data Models

### Enhanced User Model Schema
```sql
CREATE TABLE users (
    id VARCHAR(255) PRIMARY KEY,
    google_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    picture TEXT,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Update existing UUID-based records if needed
ALTER TABLE users ALTER COLUMN id TYPE VARCHAR(255);
```

### JWT Token Structure
```json
{
  "user_id": "101506588471475152713",
  "email": "<EMAIL>",
  "name": "Attia Ibrahim",
  "provider": "google",
  "is_admin": true,
  "iss": "carnow-backend",
  "sub": "101506588471475152713",
  "aud": ["carnow-app"],
  "exp": **********,
  "iat": **********,
  "nbf": **********
}
```

## Error Handling

### 1. Token Validation Errors
```go
type TokenValidationError struct {
    Type    string `json:"type"`
    Message string `json:"message"`
    Code    string `json:"code"`
}

func HandleTokenValidationError(err error) TokenValidationError {
    switch {
    case strings.Contains(err.Error(), "token is expired"):
        return TokenValidationError{
            Type:    "TOKEN_EXPIRED",
            Message: "Token has expired, please refresh",
            Code:    "AUTH_001",
        }
    case strings.Contains(err.Error(), "signature is invalid"):
        return TokenValidationError{
            Type:    "INVALID_SIGNATURE",
            Message: "Token signature is invalid",
            Code:    "AUTH_002",
        }
    default:
        return TokenValidationError{
            Type:    "VALIDATION_FAILED",
            Message: "Token validation failed",
            Code:    "AUTH_003",
        }
    }
}
```

### 2. Database Operation Fallback
```go
func (s *AuthService) AuthenticateWithFallback(googleToken string) (*AuthResponse, error) {
    user, err := s.authenticateWithDatabase(googleToken)
    if err != nil {
        log.Printf("⚠️ Database authentication failed, using fallback: %v", err)
        return s.authenticateWithFallback(googleToken)
    }
    return user, nil
}
```

## Testing Strategy

### 1. Unit Tests
- JWT token generation and validation
- User ID format conversion
- Database query with string IDs
- Error handling scenarios

### 2. Integration Tests
- Complete authentication flow
- Protected endpoint access
- Token refresh mechanism
- Database operations

### 3. End-to-End Tests
- Google OAuth login
- Garage access after authentication
- Token expiration handling
- User session management

## Implementation Priority

1. **High Priority**: JWT middleware fix and token validation
2. **High Priority**: User ID format consistency in database
3. **Medium Priority**: Enhanced error handling and logging
4. **Low Priority**: Token refresh mechanism optimization

## Security Considerations

1. **Token Security**: Use strong JWT secrets and proper signing algorithms
2. **ID Validation**: Validate Google IDs before database operations
3. **Error Disclosure**: Limit error information exposure in production
4. **Session Management**: Implement proper token expiration and refresh